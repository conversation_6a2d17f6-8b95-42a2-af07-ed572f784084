import os
from dotenv import load_dotenv
from flask import Flask, request, jsonify, session
import mysql.connector
from mysql.connector import pooling
import bcrypt
import uuid
from datetime import datetime

# Load environment variables from .env file
load_dotenv()

# Initialize Flask app
app = Flask(__name__)

# MySQL Connection Pool Configuration
db_config = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'pabbo@123'),  # Default to your current password if env var not set
    'database': os.getenv('DB_NAME', 'LawFort'),
    'pool_name': 'lawfort_pool',
    'pool_size': int(os.getenv('DB_POOL_SIZE', 5))
}

# Create connection pool
connection_pool = pooling.MySQLConnectionPool(**db_config)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your_secret_key')

# Function to get database connection from pool
def get_db_connection():
    return connection_pool.get_connection()

# Function to hash passwords
def hash_password(password):
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

# Function to check password
def check_password(stored_password, entered_password):
    # Convert stored_password to bytes if it's a string
    if isinstance(stored_password, str):
        stored_password = stored_password.encode('utf-8')
    return bcrypt.checkpw(entered_password.encode('utf-8'), stored_password)

# Function to generate session token
def generate_session_token():
    return str(uuid.uuid4())

@app.route('/register', methods=['POST'])
def register_user():
    data = request.get_json()

    email = data['email']
    password = data['password']
    full_name = data['full_name']
    phone = data['phone']
    bio = data['bio']
    profile_pic = data['profile_pic']
    law_specialization = data['law_specialization']
    education = data['education']
    bar_exam_status = data['bar_exam_status']
    license_number = data['license_number']
    practice_area = data['practice_area']
    location = data['location']
    years_of_experience = data['years_of_experience']
    linkedin_profile = data['linkedin_profile']
    alumni_of = data['alumni_of']
    professional_organizations = data['professional_organizations']

    # Hash the password and ensure it's stored as bytes
    hashed_password = hash_password(password)
    
    # Convert bytes to string for database storage if needed
    if isinstance(hashed_password, bytes):
        hashed_password = hashed_password.decode('utf-8')

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # First, ensure the roles exist
        cursor.execute("SELECT COUNT(*) FROM Roles WHERE Role_ID = 3")
        role_exists = cursor.fetchone()[0]

        if role_exists == 0:
            # Insert default roles if they don't exist
            cursor.execute("""
                INSERT IGNORE INTO Roles (Role_ID, Role_Name, Description) VALUES
                (1, 'Admin', 'System Administrator with full access'),
                (2, 'Editor', 'Content Editor with content management access'),
                (3, 'User', 'Regular User with standard access')
            """)

        # Insert user into Users table
        cursor.execute("""
            INSERT INTO Users (Email, Password, Role_ID, Status)
            VALUES (%s, %s, 3, 'Active')
        """, (email, hashed_password))

        # Get the User_ID of the newly created user
        user_id = cursor.lastrowid

        # Insert user profile into User_Profile table
        cursor.execute("""
            INSERT INTO User_Profile (User_ID, Full_Name, Phone, Bio, Profile_Pic, Law_Specialization,
                                    Education, Bar_Exam_Status, License_Number, Practice_Area, Location,
                                    Years_of_Experience, LinkedIn_Profile, Alumni_of, Professional_Organizations)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (user_id, full_name, phone, bio, profile_pic, law_specialization, education, bar_exam_status,
              license_number, practice_area, location, years_of_experience, linkedin_profile, alumni_of, professional_organizations))

        conn.commit()
        return jsonify({'message': 'Registration successful.'}), 201
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@app.route('/login', methods=['POST'])
def login_user():
    data = request.get_json()

    email = data['email']
    password = data['password']

    conn = get_db_connection()
    cursor = conn.cursor(buffered=True)

    try:
        cursor.execute("SELECT User_ID, Password FROM Users WHERE Email = %s", (email,))
        user = cursor.fetchone()

        if user and check_password(user[1], password):
            # Generate session token
            session_token = generate_session_token()

            cursor.execute("""
                INSERT INTO Session (User_ID, Session_Token, Last_Active_Timestamp)
                VALUES (%s, %s, %s)
            """, (user[0], session_token, datetime.now()))

            conn.commit()
            return jsonify({'message': 'Login successful', 'session_token': session_token}), 200
        else:
            return jsonify({'error': 'Invalid credentials'}), 401
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@app.route('/logout', methods=['POST'])
def logout_user():
    data = request.get_json()
    session_token = data['session_token']

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute("DELETE FROM Session WHERE Session_Token = %s", (session_token,))
        conn.commit()
        return jsonify({'message': 'Logout successful'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()
@app.route('/request_editor_access', methods=['POST'])
def request_editor_access():
    data = request.get_json()
    user_id = data['user_id']

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute("SELECT COUNT(*) FROM Access_Request WHERE User_ID = %s AND Status = 'Pending'", (user_id,))
        count = cursor.fetchone()[0]

        if count > 0:
            return jsonify({'error': 'You already have a pending request.'}), 400

        cursor.execute("""
            INSERT INTO Access_Request (User_ID, Status)
            VALUES (%s, 'Pending')
        """, (user_id,))

        conn.commit()
        return jsonify({'message': 'Request for editor access sent to admin.'}), 200
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()
@app.route('/admin/approve_deny_access', methods=['POST'])
def admin_approve_deny_access():
    data = request.get_json()
    request_id = data['request_id']
    action = data['action']  # 'Approve' or 'Deny'
    admin_id = data['admin_id']

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute("SELECT User_ID, Status FROM Access_Request WHERE Request_ID = %s", (request_id,))
        request = cursor.fetchone()

        if not request or request[1] != 'Pending':
            return jsonify({'error': 'Invalid or already processed request'}), 400

        if action == 'Approve':
            cursor.execute("""
                UPDATE Access_Request
                SET Status = 'Approved', Approved_At = NOW(), Admin_ID = %s
                WHERE Request_ID = %s
            """, (admin_id, request_id))

            cursor.execute("UPDATE Users SET Role_ID = 2 WHERE User_ID = %s", (request[0],))  # Set role to Editor

            message = 'Editor access granted.'
        else:
            cursor.execute("""
                UPDATE Access_Request
                SET Status = 'Denied', Denied_At = NOW(), Admin_ID = %s
                WHERE Request_ID = %s
            """, (admin_id, request_id))

            message = 'Editor access denied.'

        conn.commit()
        return jsonify({'message': message}), 200
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@app.route('/admin/access_requests', methods=['GET'])
def get_access_requests():
    conn = get_db_connection()
    cursor = conn.cursor(buffered=True)

    try:
        cursor.execute("""
            SELECT ar.Request_ID, ar.User_ID, up.Full_Name, up.Practice_Area,
                   ar.Requested_At, ar.Status
            FROM Access_Request ar
            JOIN User_Profile up ON ar.User_ID = up.User_ID
            WHERE ar.Status = 'Pending'
            ORDER BY ar.Requested_At DESC
        """)

        requests = cursor.fetchall()

        access_requests = []
        for req in requests:
            access_requests.append({
                'request_id': req[0],
                'user_id': req[1],
                'full_name': req[2],
                'practice_area': req[3],
                'requested_at': req[4].isoformat() if req[4] else None,
                'status': req[5]
            })

        return jsonify({'access_requests': access_requests}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@app.route('/user/profile', methods=['GET'])
def get_user_profile():
    session_token = request.headers.get('Authorization')
    if not session_token:
        return jsonify({'error': 'Session token required'}), 401

    # Remove 'Bearer ' prefix if present
    if session_token.startswith('Bearer '):
        session_token = session_token[7:]

    conn = get_db_connection()
    cursor = conn.cursor(buffered=True)

    try:
        # Get user ID from session token
        cursor.execute("SELECT User_ID FROM Session WHERE Session_Token = %s", (session_token,))
        session = cursor.fetchone()

        if not session:
            return jsonify({'error': 'Invalid session token'}), 401

        user_id = session[0]

        # Get user details with profile
        cursor.execute("""
            SELECT u.User_ID, u.Email, u.Role_ID, u.Status,
                   up.Full_Name, up.Phone, up.Bio, up.Profile_Pic, up.Law_Specialization,
                   up.Education, up.Bar_Exam_Status, up.License_Number, up.Practice_Area,
                   up.Location, up.Years_of_Experience, up.LinkedIn_Profile, up.Alumni_of,
                   up.Professional_Organizations, r.Role_Name
            FROM Users u
            LEFT JOIN User_Profile up ON u.User_ID = up.User_ID
            LEFT JOIN Roles r ON u.Role_ID = r.Role_ID
            WHERE u.User_ID = %s
        """, (user_id,))

        user_data = cursor.fetchone()

        if not user_data:
            return jsonify({'error': 'User not found'}), 404

        user_profile = {
            'id': str(user_data[0]),
            'email': user_data[1],
            'role_id': user_data[2],
            'role_name': user_data[18] if user_data[18] else 'User',
            'status': user_data[3],
            'full_name': user_data[4] or '',
            'phone': user_data[5] or '',
            'bio': user_data[6] or '',
            'profile_pic': user_data[7] or '',
            'law_specialization': user_data[8] or '',
            'education': user_data[9] or '',
            'bar_exam_status': user_data[10] or '',
            'license_number': user_data[11] or '',
            'practice_area': user_data[12] or '',
            'location': user_data[13] or '',
            'years_of_experience': user_data[14] or 0,
            'linkedin_profile': user_data[15] or '',
            'alumni_of': user_data[16] or '',
            'professional_organizations': user_data[17] or ''
        }

        return jsonify({'user': user_profile}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@app.route('/user/validate_session', methods=['GET'])
def validate_session():
    session_token = request.headers.get('Authorization')
    if not session_token:
        return jsonify({'error': 'Session token required'}), 401

    # Remove 'Bearer ' prefix if present
    if session_token.startswith('Bearer '):
        session_token = session_token[7:]

    conn = get_db_connection()
    cursor = conn.cursor(buffered=True)

    try:
        cursor.execute("SELECT User_ID FROM Session WHERE Session_Token = %s", (session_token,))
        session = cursor.fetchone()

        if session:
            return jsonify({'valid': True, 'user_id': session[0]}), 200
        else:
            return jsonify({'valid': False}), 401
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

# Add CORS headers for frontend integration
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

if __name__ == '__main__':
    app.run(debug=True)
